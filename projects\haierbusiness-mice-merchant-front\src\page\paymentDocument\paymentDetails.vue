<!-- 付款凭证详情页 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Table as hTable,
  Upload as hUpload,
  Modal,
  message,
  Input as hInput,
  DatePicker as hDatePicker,
  Radio as hRadio,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { computed, ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';
import { paymentFromApi, fileApi } from '@haierbusiness-front/apis';

const route = useRoute();
const router = useRouter();

// 接收参数
const routeParams = resolveParam(route.query.record as string);
const paymentId = ref(routeParams?.paymentId);
const paymentCode = ref(routeParams?.paymentCode);

// 页面数据
const paymentDetail = ref<any>(null);
const loading = ref(false);
const uploadLoading = ref(false);
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 申请文件类型选择
const fileTypeOption = ref('agree');

// 发票相关
const invoiceList = ref<any[]>([
  {
    id: 1,
    serialNumber: 1,
    invoiceNumber: '85645433',
    invoiceDate: '2025.1.21',
    invoiceAmount: 334.75,
    file: null,
  },
  {
    id: 2,
    serialNumber: 2,
    invoiceNumber: '85645434',
    invoiceDate: '2025.1.21',
    invoiceAmount: 334.75,
    file: null,
  },
  {
    id: 3,
    serialNumber: 3,
    invoiceNumber: '85645435',
    invoiceDate: '2025.1.21',
    invoiceAmount: 334.75,
    file: null,
  },
]);

// 模拟付款详情数据
const mockPaymentDetail = {
  paymentCode: 'D3EC20250212132380004',
  merchantName: '国海商务会议服务有限公司',
  totalAmount: 2400,
  paymentRecordsDetails: [
    {
      meetingCode: 'MC2024110171919180001',
      meetingTime: '2024-11-29 至 2024-12-01',
      manager: '张三',
      meetingAmount: 1000,
      paymentRate: 80,
      paymentAmount: 800,
    },
    {
      meetingCode: 'MC2024110171919180002',
      meetingTime: '2024-11-29 至 2024-12-01',
      manager: '张三',
      meetingAmount: 1000,
      paymentRate: 80,
      paymentAmount: 800,
    },
    {
      meetingCode: 'MC2024110171919180003',
      meetingTime: '2024-11-29 至 2024-12-01',
      manager: '张三',
      meetingAmount: 1000,
      paymentRate: 80,
      paymentAmount: 800,
    },
  ],
};

onMounted(() => {
  // 模拟获取详情数据
  paymentDetail.value = mockPaymentDetail;

  // 如果需要调用真实接口，取消注释
  // if (paymentId.value) {
  //   fetchPaymentDetail();
  // }
});

// 获取付款详情
const fetchPaymentDetail = async () => {
  try {
    loading.value = true;
    // const response = await paymentFromApi.getDetails(paymentId.value);
    // paymentDetail.value = response;
  } catch (error) {
    message.error('获取详情失败');
  } finally {
    loading.value = false;
  }
};

// 账单详情表格列
const billColumns: ColumnType[] = [
  {
    title: '会议编号',
    dataIndex: 'meetingCode',
    width: '200px',
    align: 'center',
  },
  {
    title: '会议时间',
    dataIndex: 'meetingTime',
    width: '200px',
    align: 'center',
  },
  {
    title: '会议负责人',
    dataIndex: 'manager',
    width: '120px',
    align: 'center',
  },
  {
    title: '会议金额',
    dataIndex: 'meetingAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => `${text}元`,
  },
  {
    title: '付款比例',
    dataIndex: 'paymentRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => `${text}%`,
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => `${text}元`,
  },
];

// 发票表格列
const invoiceColumns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'serialNumber',
    width: '80px',
    align: 'center',
  },
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    width: '150px',
    align: 'center',
  },
  {
    title: '过票日期',
    dataIndex: 'invoiceDate',
    width: '120px',
    align: 'center',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => `${text}元`,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '100px',
    align: 'center',
  },
];

// 计算发票总金额
const totalInvoiceAmount = computed(() => {
  const total = invoiceList.value.reduce((sum, item) => sum + (item.invoiceAmount || 0), 0);
  return total.toFixed(2);
});

// 添加发票
const addInvoice = () => {
  const newId = Math.max(...invoiceList.value.map((item) => item.id)) + 1;
  const newSerialNumber = invoiceList.value.length + 1;

  invoiceList.value.push({
    id: newId,
    serialNumber: newSerialNumber,
    invoiceNumber: '',
    invoiceDate: '',
    invoiceAmount: 0,
    file: null,
  });
};

// 删除发票
const deleteInvoice = (record: any) => {
  const index = invoiceList.value.findIndex((item) => item.id === record.id);
  if (index > -1) {
    invoiceList.value.splice(index, 1);
    // 重新计算序号
    invoiceList.value.forEach((item, idx) => {
      item.serialNumber = idx + 1;
    });
  }
};

// 文件上传处理
const handleUpload = (record: any, options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  // 模拟上传成功
  setTimeout(() => {
    record.file = {
      name: options.file.name,
      path: `/files/${options.file.name}`,
    };
    options.onSuccess();
    uploadLoading.value = false;
    message.success('文件上传成功');
  }, 1000);

  // 真实上传接口
  // fileApi.upload(formData)
  //   .then((response) => {
  //     record.file = {
  //       name: options.file.name,
  //       path: baseUrl + response.path,
  //     };
  //     options.onSuccess(response);
  //     message.success('文件上传成功');
  //   })
  //   .catch((error) => {
  //     options.onError(error);
  //     message.error('文件上传失败');
  //   })
  //   .finally(() => {
  //     uploadLoading.value = false;
  //   });
};

// 提交
const handleSubmit = () => {
  // 验证必填项
  const hasEmptyFields = invoiceList.value.some(
    (item) => !item.invoiceNumber || !item.invoiceDate || !item.invoiceAmount || !item.file,
  );

  if (hasEmptyFields) {
    message.error('请完善所有发票信息并上传文件');
    return;
  }

  loading.value = true;

  // 模拟提交
  setTimeout(() => {
    message.success('提交成功');
    loading.value = false;
    // 返回列表页
    router.push('/mice-merchant/paymentDocument/paymentOrderList');
  }, 1000);

  // 真实提交接口
  // const submitData = {
  //   paymentId: paymentId.value,
  //   invoices: invoiceList.value.map(item => ({
  //     invoiceNumber: item.invoiceNumber,
  //     invoiceDate: item.invoiceDate,
  //     invoiceAmount: item.invoiceAmount,
  //     filePath: item.file?.path,
  //   })),
  // };
  //
  // paymentFromApi.submitPaymentProof(submitData)
  //   .then(() => {
  //     message.success('提交成功');
  //     router.push('/mice-merchant/paymentDocument/paymentOrderList');
  //   })
  //   .catch(() => {
  //     message.error('提交失败');
  //   })
  //   .finally(() => {
  //     loading.value = false;
  //   });
};

// 返回
const goBack = () => {
  router.push('/mice-merchant/paymentDocument/paymentOrderList');
};
</script>

<template>
  <div class="payment-details-container">
    <h-row :gutter="24" style="height: 100%">
      <!-- 左侧：账单详情 -->
      <h-col :span="12" class="left-panel">
        <div class="panel-header">
          <h3>付款单详情</h3>
        </div>

        <div class="detail-info">
          <div class="info-item">
            <span class="label">付款单号：</span>
            <span class="value">{{ paymentDetail?.paymentCode }}</span>
          </div>
          <div class="info-item">
            <span class="label">供应商：</span>
            <span class="value">{{ paymentDetail?.merchantName }}</span>
          </div>
          <div class="info-item">
            <span class="label">收款总金额：</span>
            <span class="value amount">{{ paymentDetail?.totalAmount }}元</span>
          </div>
        </div>

        <h-table
          :columns="billColumns"
          :data-source="paymentDetail?.paymentRecordsDetails || []"
          :pagination="false"
          size="small"
          bordered
          :scroll="{ y: 300 }"
        />
      </h-col>

      <!-- 右侧：发票上传 -->
      <h-col :span="12" class="right-panel">
        <div class="panel-header">
          <h3>申请单发票上传</h3>
        </div>

        <div class="upload-section">
          <div class="file-type">
            <span class="label">审批意见：</span>
            <h-radio-group v-model:value="fileTypeOption" class="file-type-radio">
              <h-radio value="agree">通过</h-radio>
              <h-radio value="reject">驳回</h-radio>
            </h-radio-group>
          </div>

          <h-table
            :columns="invoiceColumns"
            :data-source="invoiceList"
            :pagination="false"
            size="small"
            bordered
            :scroll="{ y: 300 }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'invoiceNumber'">
                <span>{{ record.invoiceNumber }}</span>
              </template>

              <template v-if="column.dataIndex === 'invoiceDate'">
                <span>{{ record.invoiceDate }}</span>
              </template>

              <template v-if="column.dataIndex === 'invoiceAmount'">
                <span>{{ record.invoiceAmount }}元</span>
              </template>

              <template v-if="column.dataIndex === 'action'">
                <div class="action-buttons">
                  <h-button
                    size="small"
                    type="link"
                    danger
                    @click="deleteInvoice(record)"
                    :disabled="invoiceList.length <= 1"
                  >
                    <DeleteOutlined />
                    删除
                  </h-button>
                </div>
              </template>
            </template>
          </h-table>

          <div class="add-invoice-section">
            <h-button type="dashed" @click="addInvoice" class="add-invoice-btn">
              <PlusOutlined />
              添加发票
            </h-button>
          </div>

          <div class="total-section">
            <span class="total-label">合计：</span>
            <span class="total-amount">{{ totalInvoiceAmount }}元</span>
          </div>
        </div>

        <div class="footer-actions">
          <h-button @click="goBack" style="margin-right: 12px"> 返回 </h-button>
          <h-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </h-button>
        </div>
      </h-col>
    </h-row>
  </div>
</template>

<style scoped lang="less">
.payment-details-container {
  height: 100vh;
  padding: 16px;
  background-color: #f5f5f5;
}

.left-panel,
.right-panel {
  height: 100%;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.panel-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 12px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.detail-info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    font-weight: 500;
    color: #666;
    width: 100px;
    flex-shrink: 0;
  }

  .value {
    color: #333;

    &.amount {
      color: #f56a00;
      font-weight: 600;
    }
  }
}

.upload-section {
  margin-bottom: 20px;
}

.section-title {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.file-type {
  margin-bottom: 16px;
  display: flex;
  align-items: center;

  .label {
    font-weight: 500;
    color: #666;
    margin-right: 12px;
  }

  .value {
    color: #333;
    margin-left: 8px;
  }

  .file-type-radio {
    margin-left: 0;
  }
}

.action-buttons {
  display: flex;
  flex-direction: row;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.add-invoice-section {
  margin: 16px 0;
  text-align: center;
}

.add-invoice-btn {
  width: 100%;
  height: 40px;
  border-style: dashed;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }
}

.total-section {
  margin: 16px 0;
  text-align: right;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;

  .total-label {
    font-weight: 500;
    color: #666;
  }

  .total-amount {
    font-weight: 600;
    color: #f56a00;
    font-size: 16px;
  }
}

.footer-actions {
  margin-top: 20px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px !important;
}

:deep(.ant-input),
:deep(.ant-picker) {
  border-radius: 4px;
}
</style>
